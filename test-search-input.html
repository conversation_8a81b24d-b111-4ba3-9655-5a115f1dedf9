<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Input Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Simulate the muted-foreground color */
        .text-muted-foreground {
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-8">Search Input Test - Hotel Management</h1>
        
        <!-- Before Fix (Broken) -->
        <div class="mb-8 p-6 bg-white rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4 text-red-600">❌ Before Fix (Broken)</h2>
            <p class="text-sm text-gray-600 mb-4">Icon overlaps with text input area - difficult to type</p>
            <div class="flex flex-col md:flex-row gap-4">
                <div class="relative flex-grow">
                    <!-- MagnifyingGlass icon positioned at left-3 -->
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <!-- Input WITHOUT proper left padding -->
                    <input
                        type="text"
                        placeholder="Search hotels..."
                        class="h-9 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
            </div>
        </div>

        <!-- After Fix (Working) -->
        <div class="mb-8 p-6 bg-white rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4 text-green-600">✅ After Fix (Working)</h2>
            <p class="text-sm text-gray-600 mb-4">Icon has proper spacing - text input works correctly</p>
            <div class="flex flex-col md:flex-row gap-4">
                <div class="relative flex-grow">
                    <!-- MagnifyingGlass icon positioned at left-3 -->
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <!-- Input WITH proper left padding (pl-10) -->
                    <input
                        type="text"
                        placeholder="Search hotels..."
                        class="h-9 pl-10 pr-3 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
            </div>
        </div>

        <!-- Explanation -->
        <div class="p-6 bg-blue-50 rounded-lg">
            <h2 class="text-lg font-semibold mb-4">🔧 Fix Explanation</h2>
            <div class="space-y-2 text-sm">
                <p><strong>Problem:</strong> The search icon was positioned at <code>left-3</code> (0.75rem) but the input had no left padding to account for the icon.</p>
                <p><strong>Solution:</strong> Added <code>pl-10</code> (2.5rem left padding) to the input to provide proper spacing for the icon.</p>
                <p><strong>Pattern:</strong> This follows the same pattern used in other search inputs throughout the codebase.</p>
            </div>
        </div>

        <!-- Test Instructions -->
        <div class="mt-8 p-6 bg-yellow-50 rounded-lg">
            <h2 class="text-lg font-semibold mb-4">🧪 Test Instructions</h2>
            <ol class="list-decimal list-inside space-y-2 text-sm">
                <li>Try typing in the "Before Fix" input - notice how the cursor starts under the icon</li>
                <li>Try typing in the "After Fix" input - notice how the cursor starts after the icon</li>
                <li>The fix ensures proper text input functionality and visual alignment</li>
            </ol>
        </div>
    </div>

    <script>
        // Add some interactivity to demonstrate the fix
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input[type="text"]');
            inputs.forEach((input, index) => {
                input.addEventListener('input', function(e) {
                    console.log(`Input ${index + 1} value:`, e.target.value);
                });
                
                input.addEventListener('focus', function(e) {
                    console.log(`Input ${index + 1} focused`);
                });
            });
        });
    </script>
</body>
</html>
