import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Buildings,
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import {
  Upload as UploadIcon,
  Download,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import OutlineButton from "../../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  FocusModal,
  Toaster,
  toast,
  Select,
} from "@camped-ai/ui";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./[slug]/modal-fix.css"; // Import custom CSS to fix z-index issues with modals
import { HotelFormData } from "../../../components/hotel-form-modern";
import HotelFormModern from "../../../components/hotel-form-modern";
import { HotelData } from "../../../types";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import BulkImportModal from "../../../components/hotel/bulk-import-modal";
import ExportModal from "../../../components/hotel/export-modal";

import { useRbac } from "../../../hooks/use-rbac";
import HotelCard from "../../../components/hotel-management/HotelCard";
import HotelListItem from "../../../components/hotel-management/HotelListItem";
import { useImagePreloader } from "../../../hooks/useImagePreloader";
import { useImagePerformance } from "../../../hooks/useImagePerformance";
import { useHotelManagementWithDestinations } from "../../../hooks/hotel-management";
import { Combobox } from "../../../components/common/combobox";

interface Hotel extends Omit<HotelData, "description" | "website" | "email"> {
  destination_name?: string;
  image_url?: string;
  star?: number;
  is_internal?: boolean;
  is_featured?: boolean;
  description?: string; // Override to make optional to match API response
  website?: string; // Override to match API response (undefined instead of null)
  email?: string; // Override to match API response (undefined instead of null)
}

const HotelsPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  console.log(
    "Has permission to create hotel:",
    hasPermission("hotel_management:create")
  );
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Pagination state - optimized for better performance
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 10; // Reduced from 21 to 10 for better performance
  const [formData, setFormData] = useState<HotelFormData>({
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    is_pets_allowed: false,
    website: null,
    email: null,
    destination_id: "",
    check_in_time: "14:00",
    check_out_time: "11:00",
    media: [], // Initialize with empty media array
    image_ids: [], // Initialize with empty image IDs array
  });
  // Legacy state - will be replaced by TanStack Query
  const [destinations, setDestinations] = useState<
    { id: string; name: string }[]
  >([]);
  // Room types state removed - not needed for this page

  // Direct URL parameter handling using useSearchParams
  const [searchParams, setSearchParams] = useSearchParams();

  // Helper functions to get URL parameters
  const getParam = useCallback((key: string) => searchParams.get(key), [searchParams]);





  // Helper functions to update URL parameters
  const updateParams = useCallback((updates: Record<string, any>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === undefined || value === "") {
        newParams.delete(key);
      } else if (Array.isArray(value)) {
        if (value.length === 0) {
          newParams.delete(key);
        } else {
          newParams.set(key, value.join(","));
        }
      } else {
        newParams.set(key, String(value));
      }
    });
    setSearchParams(newParams, { replace: true });
  }, [searchParams, setSearchParams]);

  const updateParamsImmediate = useCallback((updates: Record<string, any>) => {
    updateParams(updates);
  }, [updateParams]);

  // Separate input state from URL state for immediate feedback
  const [searchInput, setSearchInput] = useState(getParam("search") || "");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(
    getParam("search") || ""
  );

  // UI states - read directly from URL for simplicity
  const showFilters = useMemo(() => {
    const filtersParam = searchParams.get("filters");
    return filtersParam === "true";
  }, [searchParams]);

  const viewMode = useMemo(() => {
    const viewParam = searchParams.get("view");
    return (viewParam as "grid" | "list") || "grid";
  }, [searchParams]);

  // Image optimization hooks
  const { logPerformanceStats } = useImagePerformance();

  // Log performance stats periodically (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      const interval = setInterval(() => {
        logPerformanceStats();
      }, 30000); // Log every 30 seconds

      return () => clearInterval(interval);
    }
  }, [logPerformanceStats]);

  // Memoize URL parameter values to prevent constant re-renders
  const currentPage = useMemo(() => {
    const pageParam = searchParams.get("page");
    return parseInt(pageParam || "1", 10);
  }, [searchParams]);

  const limit = useMemo(() => {
    const limitParam = searchParams.get("limit");
    return parseInt(limitParam || pageSize.toString(), 10);
  }, [searchParams, pageSize]);

  // Memoize filter values to prevent TanStack Query from refetching constantly
  const filterDestination = useMemo(() => {
    const destParam = searchParams.get("destination");
    return destParam || undefined;
  }, [searchParams]);

  const filterStars = useMemo(() => {
    const starsParam = searchParams.get("stars");
    return starsParam ? starsParam.split(",").map(Number).filter(n => !isNaN(n)) : [];
  }, [searchParams]);

  const filterFeatured = useMemo(() => {
    const featuredParam = searchParams.get("featured");
    return featuredParam === "true" ? true : featuredParam === "false" ? false : null;
  }, [searchParams]);

  // Memoize search value for stable reference
  const urlSearchValue = useMemo(() => {
    const searchParam = searchParams.get("search");
    return searchParam || "";
  }, [searchParams]);

  // Debounce search query for filtering and URL updates
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchInput);
      updateParams({ search: searchInput });
    }, 300);

    return () => clearTimeout(timer);
  }, [searchInput, updateParams]);

  // Sync input with URL changes (browser back/forward, direct URL access)
  useEffect(() => {
    if (urlSearchValue !== searchInput) {
      setSearchInput(urlSearchValue);
      setDebouncedSearchQuery(urlSearchValue);
    }
  }, [urlSearchValue, searchInput]);

  // TanStack Query hooks for optimized data fetching
  // This replaces multiple separate API calls with efficient combined queries
  const {
    hotels: queryHotels,
    isLoading: queryIsLoading,
    destinationsData,
    totalCount: queryTotalCount,
  } = useHotelManagementWithDestinations({
    limit,
    offset: (currentPage - 1) * limit,
    is_featured: filterFeatured !== null ? filterFeatured : undefined,
    destination_id: filterDestination || undefined,
    search: debouncedSearchQuery || undefined,
    star_rating: filterStars.length > 0 ? filterStars : undefined, // Add star rating filter
  });

  // Room types removed - not needed for this page

  // Use TanStack Query data when available, fallback to legacy state
  const effectiveHotels = queryHotels.length > 0 ? queryHotels : hotels;
  const effectiveIsLoading = queryIsLoading || isLoading;
  const effectiveTotalCount = queryTotalCount || totalCount;
  const effectiveDestinations = destinationsData?.destinations || destinations;

  // Update legacy state when TanStack Query data is available
  useEffect(() => {
    if (
      destinationsData?.destinations &&
      destinationsData.destinations.length > 0
    ) {
      setDestinations(
        destinationsData.destinations.map((dest) => ({
          id: dest.id,
          name: dest.name,
        }))
      );
    }
  }, [destinationsData]);

  // Room types useEffect removed - not needed for this page

  useEffect(() => {
    if (queryHotels.length > 0) {
      setHotels(queryHotels);
      setTotalCount(queryTotalCount);
      setIsLoading(false);
    }
  }, [queryHotels, queryTotalCount]);

  // Extract image URLs for preloading using effective hotels
  const actualPriorityImages = effectiveHotels
    .slice(0, viewMode === "grid" ? 6 : 3)
    .map((hotel) => hotel.image_url)
    .filter((url): url is string => Boolean(url));

  const actualNonPriorityImages = effectiveHotels
    .slice(viewMode === "grid" ? 6 : 3)
    .map((hotel) => hotel.image_url)
    .filter((url): url is string => Boolean(url));

  // Preload priority images immediately, others with delay
  useImagePreloader({ images: actualPriorityImages, priority: true });
  useImagePreloader({ images: actualNonPriorityImages, priority: false });

  // Modal/Drawer states with URL synchronization
  const open = useMemo(() => searchParams.get("add") === "true", [searchParams]);
  const bulkImportOpen = useMemo(() => searchParams.get("import") === "true", [searchParams]);
  const exportModalOpen = useMemo(() => searchParams.get("export") === "true", [searchParams]);

  // Listen for custom events
  useEffect(() => {
    const handleCloseModal = (event: any) => {
      updateBulkImportOpen(false);
      // Only refresh if explicitly requested
      if (event.detail?.refresh) {
        setTimeout(() => {
          console.log("Refreshing hotels after import");
          // Will be handled by the URL parameter change effect
        }, 500);
      }
    };

    const handleRefreshData = () => {
      // Refresh data without closing the modal
      setTimeout(() => {
        console.log("Refreshing hotels after successful import");
        // Will be handled by the URL parameter change effect
      }, 500);
    };

    window.addEventListener("closeHotelModal", handleCloseModal);
    window.addEventListener("refreshHotelData", handleRefreshData);

    return () => {
      window.removeEventListener("closeHotelModal", handleCloseModal);
      window.removeEventListener("refreshHotelData", handleRefreshData);
    };
  }, [destinations]);

  // Ref to access the form's submit function
  const formSubmitRef = useRef<(() => Promise<void>) | null>(null);

  // Optimized update functions using the custom hook
  const updateShowFilters = useCallback(
    (value: boolean) => {
      updateParamsImmediate({ filters: value }); // Immediate URL update for UI responsiveness
    },
    [updateParamsImmediate]
  );

  const updateFilterDestination = useCallback(
    (value: string | undefined) => {
      updateParams({ destination: value || "" });
    },
    [updateParams]
  );

  const updateFilterStars = useCallback(
    (value: number[]) => {
      updateParams({ stars: value });
    },
    [updateParams]
  );

  const updateFilterFeatured = useCallback(
    (value: boolean | null) => {
      updateParams({ featured: value });
    },
    [updateParams]
  );

  const updateViewMode = useCallback(
    (value: "grid" | "list") => {
      updateParamsImmediate({ view: value }); // Immediate URL update for UI responsiveness
    },
    [updateParamsImmediate]
  );

  const updateOpen = useCallback(
    (value: boolean) => {
      updateParams({ add: value });
    },
    [updateParams]
  );

  const updateBulkImportOpen = useCallback(
    (value: boolean) => {
      updateParams({ import: value });
    },
    [updateParams]
  );

  const updateExportModalOpen = useCallback(
    (value: boolean) => {
      updateParams({ export: value });
    },
    [updateParams]
  );

  // Legacy fetch functions removed - now handled by TanStack Query hooks
  // fetchDestinations() -> useDestinationsForHotelManagement()
  // fetchRoomTypes() -> removed (not needed for this page)

  // fetchDestinationDetails removed - destinations are now pre-loaded via TanStack Query

  // fetchHotels function removed - now handled by useHotelManagementWithDestinations hook

  // All hotel fetching logic removed - now handled by TanStack Query hooks

  // Create a stable reference for filter values to detect actual changes
  const filterValues = useMemo(() => ({
    destination: filterDestination,
    featured: filterFeatured,
    stars: filterStars,
    search: debouncedSearchQuery,
  }), [filterDestination, filterFeatured, filterStars, debouncedSearchQuery]);

  // Track previous filter values to detect changes
  const previousFilters = useRef(filterValues);

  // Reset to page 1 only when filters actually change (not when they're just active)
  useEffect(() => {
    const prev = previousFilters.current;

    // Check if any filter actually changed - using deep comparison for arrays
    const filtersChanged =
      prev.destination !== filterValues.destination ||
      prev.featured !== filterValues.featured ||
      JSON.stringify(prev.stars) !== JSON.stringify(filterValues.stars) ||
      prev.search !== filterValues.search;

    // Debug logging (development only)
    if (process.env.NODE_ENV === "development") {
      console.log("🔍 Filter change check:", {
        filtersChanged,
        currentPage,
        previousFilters: prev,
        currentFilters: filterValues,
        willResetPage: filtersChanged && currentPage !== 1
      });
    }

    // Only reset to page 1 if filters changed AND we're not already on page 1
    if (filtersChanged && currentPage !== 1) {
      console.log("📄 Resetting to page 1 due to filter change");
      updateParamsImmediate({ page: "1" });
    }

    // Update previous filters for next comparison
    previousFilters.current = filterValues;
  }, [filterValues, currentPage, updateParamsImmediate]);

  // Pagination handlers - using URL parameters
  const handlePageChange = useCallback(
    (page: number) => {
      // Debug logging (development only)
      if (process.env.NODE_ENV === "development") {
        console.log("🔄 Page change requested:", {
          fromPage: currentPage,
          toPage: page,
          limit,
          hasFilters: !!(filterDestination || filterFeatured !== null || filterStars.length > 0 || debouncedSearchQuery)
        });
      }

      updateParamsImmediate({
        page: page.toString(),
        limit: limit.toString(),
      });
      // TanStack Query will automatically refetch when URL params change
    },
    [updateParamsImmediate, limit, currentPage, filterDestination, filterFeatured, filterStars, debouncedSearchQuery]
  );

  const totalPages = Math.ceil(effectiveTotalCount / limit);

  const handleCreate = async (data: HotelFormData) => {
    try {
      // Separate files to upload from existing URLs
      const mediaArray = data.media ?? [];
      const filesToUpload = mediaArray.filter(
        (media) => media.file instanceof File
      );
      // Prepare data for submission without media
      const dataWithoutMedia = { ...data };
      delete dataWithoutMedia.media;
      delete dataWithoutMedia.image_ids;

      const response = await fetch("/admin/hotel-management/hotels", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataWithoutMedia),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to create hotel: ${response.status} ${response.statusText}`
        );
      }

      const responseData = await response.json();

      // Upload new files if any
      if (filesToUpload.length > 0) {
        const uploadPromises = filesToUpload.map(async (mediaFile) => {
          if (!(mediaFile.file instanceof File)) {
            throw new Error("Invalid file");
          }

          const formData = new FormData();
          formData.append("files", mediaFile.file);

          try {
            const hotelId = responseData?.hotel?.id;
            if (!hotelId) {
              throw new Error("No hotel ID available for image upload");
            }

            const response = await fetch(
              `/admin/hotel-management/hotels/${hotelId}/upload`,
              {
                method: "POST",
                body: formData,
                credentials: "include",
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error("Upload error response:", errorText);
              throw new Error(`File upload failed: ${errorText}`);
            }

            const uploadedFiles = await response.json();
            const uploadedFile = uploadedFiles[0];

            return {
              ...uploadedFile,
              isThumbnail: mediaFile.isThumbnail,
            };
          } catch (error) {
            console.error("File upload error:", error);
            throw error;
          }
        });

        await Promise.all(uploadPromises);
      }

      if (responseData.hotel) {
        // TanStack Query will automatically refetch hotels after mutation

        toast.success("Success", {
          description: "Hotel created successfully",
        });

        updateOpen(false);
        // Reset form data completely, including media array
        setFormData({
          name: "",
          handle: "",
          description: "",
          is_active: true,
          is_featured: false,
          is_pets_allowed: false,
          website: null,
          email: null,
          destination_id: "",
          check_in_time: "14:00",
          check_out_time: "11:00",
          media: [], // Explicitly reset media array
          image_ids: [], // Reset image IDs
          thumbnail_image_id: undefined, // Reset thumbnail
        });

        return true; // Return true on success
      } else {
        throw new Error("No hotel data returned from API");
      }
    } catch (error) {
      console.error("Error creating hotel:", error);
      toast.error("Error", {
        description: "Failed to create hotel",
      });
      return false; // Return false on error
    }
  };

  // All filtering is now handled server-side at the database level
  // No client-side filtering needed - use effectiveHotels directly
  const filteredHotels = effectiveHotels;

  const toggleStarFilter = useCallback(
    (star: number) => {
      if (filterStars.includes(star)) {
        updateFilterStars(filterStars.filter((s) => s !== star));
      } else {
        updateFilterStars([...filterStars, star]);
      }
    },
    [filterStars, updateFilterStars]
  );

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Heading level="h1" className="text-2xl">
              Hotels
            </Heading>
            <Text className="text-muted-foreground">
              Manage your hotel properties
            </Text>
          </div>

          <div className="flex gap-2">
            <OutlineButton
              size="small"
              onClick={() => updateExportModalOpen(true)}
              className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </OutlineButton>
            {hasPermission("hotel_management:bulk_import") && (
              <OutlineButton
                size="small"
                onClick={() => updateBulkImportOpen(true)}
                className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
              >
                <UploadIcon className="w-4 h-4" />
                <span>Import</span>
              </OutlineButton>
            )}
            {hasPermission("hotel_management:create") && (
              <Button
                variant="primary"
                size="small"
                onClick={() => updateOpen(true)}
                className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
              >
                <PlusMini className="w-4 h-4" />
                <span>Add Hotel</span>
              </Button>
            )}
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <MagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
            <Input
              placeholder="Search hotels..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="h-9 pl-10"
            />
          </div>

          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="small"
              onClick={() => updateShowFilters(!showFilters)}
              className="whitespace-nowrap bg-background border border-border shadow-sm hover:bg-muted flex items-center gap-2 px-3 py-2 rounded-md transition-all w-32"
            >
              <Adjustments className="w-4 h-4 text-muted-foreground" />
              <span>{showFilters ? "Hide Filters" : "Show Filters"}</span>
            </Button>

            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "primary" : "secondary"}
                size="small"
                onClick={() => updateViewMode("grid")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </Button>

              <Button
                variant={viewMode === "list" ? "primary" : "secondary"}
                size="small"
                onClick={() => updateViewMode("list")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
          </div>
        </div>

        {/* Active filters summary - only shown when filters are applied */}
        {(filterDestination ||
          filterStars.length > 0 ||
          filterFeatured !== null ||
          debouncedSearchQuery) &&
          !showFilters && (
            <div className="flex flex-wrap items-center gap-1.5 bg-blue-50 dark:bg-blue-950/30 border border-blue-100 dark:border-blue-800 rounded-md p-2 text-xs">
              <span className="font-medium text-blue-700 dark:text-blue-300">
                Active filters:
              </span>

              {debouncedSearchQuery && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>Search: "{debouncedSearchQuery}"</span>
                  <button
                    onClick={() => {
                      setSearchInput("");
                      setDebouncedSearchQuery("");
                      updateParams({ search: "" });
                    }}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterDestination && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>
                    Destination:{" "}
                    {destinations.find((d) => d.id === filterDestination)
                      ?.name || "Unknown"}
                  </span>
                  <button
                    onClick={() => updateFilterDestination(undefined)}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterStars.length > 0 && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>Stars: {filterStars.sort().join(", ")}★</span>
                  <button
                    onClick={() => updateFilterStars([])}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterFeatured !== null && (
                <div className="bg-background border border-blue-200 dark:border-blue-700 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 dark:text-blue-300 shadow-sm">
                  <span>
                    Status: {filterFeatured ? "Featured" : "Not Featured"}
                  </span>
                  <button
                    onClick={() => updateFilterFeatured(null)}
                    className="ml-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              <button
                onClick={() => updateShowFilters(true)}
                className="ml-auto bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/50 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-md transition-colors flex items-center gap-1 text-xs"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM9 15a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Edit Filters</span>
              </button>
            </div>
          )}

        {showFilters && (
          <div className="bg-card border border-border rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between mb-3 border-b border-border pb-2">
              <Text className="font-medium text-card-foreground">
                Filter Hotels
              </Text>
              <button
                onClick={() => updateShowFilters(false)}
                className="text-muted-foreground hover:text-foreground"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>

            {/* First row: Destination and Rating filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <Text className="font-medium text-sm text-card-foreground">
                    Destination
                  </Text>
                </div>
                <Combobox
                  value={filterDestination || ""}
                  onChange={(value) => {
                    updateFilterDestination(value || undefined);
                  }}
                  options={[
                    { value: "", label: "All Destinations" },
                    ...effectiveDestinations.map((destination) => ({
                      value: destination.id,
                      label: destination.name,
                    }))
                  ]}
                  placeholder="All Destinations"
                  allowClear
                  className="w-full h-8 text-sm"
                />
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                  <Text className="font-medium text-sm text-card-foreground">
                    Star Rating
                  </Text>
                </div>
                <div className="bg-muted">
                  <div className="flex flex-wrap gap-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        onClick={() => toggleStarFilter(star)}
                        className={`px-4 py-2 rounded-md transition-all flex items-center gap-1 text-xs ${
                          filterStars.includes(star)
                            ? "bg-yellow-400 text-yellow-900 shadow-sm border border-yellow-500 dark:bg-yellow-500 dark:text-yellow-950"
                            : "bg-background hover:bg-accent border border-border text-foreground"
                        }`}
                      >
                        <span>
                          {star} Star{star !== 1 ? "s" : ""}
                        </span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Second row: Featured Status filter */}
            <div className="mt-3">
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-muted-foreground"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                    />
                  </svg>
                  <Text className="font-medium text-sm text-card-foreground">
                    Featured Status
                  </Text>
                </div>
                <div className="bg-muted p-2">
                  <div className="flex flex-wrap gap-3">
                    <button
                      onClick={() =>
                        updateFilterFeatured(
                          filterFeatured === true ? null : true
                        )
                      }
                      className={`px-3 py-1.5 rounded-md transition-all flex items-center gap-1 flex-1 justify-center text-xs ${
                        filterFeatured === true
                          ? "bg-purple-100 text-purple-800 border border-purple-300 shadow-sm dark:bg-purple-600 dark:text-purple-100 dark:border-purple-500"
                          : "bg-background hover:bg-accent border border-border text-foreground"
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span>Featured</span>
                    </button>
                    <button
                      onClick={() =>
                        updateFilterFeatured(
                          filterFeatured === false ? null : false
                        )
                      }
                      className={`px-3 py-1.5 rounded-md transition-all flex items-center gap-1 flex-1 justify-center text-xs ${
                        filterFeatured === false
                          ? "bg-gray-700 text-white border border-gray-600 shadow-sm dark:bg-gray-300 dark:text-gray-800 dark:border-gray-400"
                          : "bg-background hover:bg-accent border border-border text-foreground"
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>Not Featured</span>
                    </button>
                  </div>
                </div>
              </div>


            </div>

            <div className="flex justify-between mt-3 pt-3 border-t border-border">
              <div>
                {(filterDestination ||
                  filterStars.length > 0 ||
                  filterFeatured !== null ||
                  debouncedSearchQuery) && (
                  <div className="text-sm text-muted-foreground">
                    <span className="font-medium">{effectiveTotalCount}</span>{" "}
                    hotels match your{" "}
                    {debouncedSearchQuery ? "search and filters" : "filters"}
                  </div>
                )}
              </div>
              <div className="flex gap-3">
                {/* <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setShowFilters(false)}
                  className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 px-3 py-1.5 rounded-md transition-all text-gray-700 text-sm"
                >
                  <span>Close</span>
                </Button> */}

                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    updateFilterDestination("");
                    updateFilterStars([]);
                    updateFilterFeatured(null);
                    setSearchInput("");
                    setDebouncedSearchQuery("");
                    updateParams({ search: "" });
                  }}
                  className="bg-background border border-border shadow-sm hover:bg-muted flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-sm"
                  disabled={
                    !filterDestination &&
                    filterStars.length === 0 &&
                    filterFeatured === null &&
                    !debouncedSearchQuery
                  }
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-3.5 h-3.5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>Clear</span>
                </Button>
              </div>
            </div>
          </div>
        )}

        {effectiveIsLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div
                key={i}
                className="h-64 animate-pulse bg-muted rounded-lg"
              ></div>
            ))}
          </div>
        ) : filteredHotels.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredHotels.map((hotel, index) => (
                <HotelCard
                  key={hotel.id}
                  hotel={{
                    ...hotel,
                    status: hotel.is_active ? "active" : "inactive",
                  }}
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${hotel.id}`)
                  }
                  priority={index < 6} // Prioritize first 6 images (2 rows)
                />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHotels.map((hotel, index) => (
                <HotelListItem
                  key={hotel.id}
                  hotel={{
                    ...hotel,
                    status: hotel.is_active ? "active" : "inactive",
                  }}
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${hotel.id}`)
                  }
                  priority={index < 3} // Prioritize first 3 images in list view
                />
              ))}
            </div>
          )
        ) : (
          <div className="flex flex-col items-center text-center py-12 bg-muted rounded-lg">
            <div className="flex items-center">
              <Buildings className="w-8 h-8 text-muted-foreground mb-0.5" />
              <Text className="text-muted-foreground mb-4">
                {debouncedSearchQuery ||
                filterDestination ||
                filterStars.length > 0 ||
                filterFeatured !== null
                  ? "No hotels match your search criteria"
                  : "No hotels found"}
              </Text>
            </div>
            {hasPermission("hotel_management:create") && (
              <OutlineButton
                variant="transparent"
                size="small"
                onClick={() => updateOpen(true)}
                className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
              >
                <PlusMini className="w-4 h-4" />
                <span>Add your first hotel</span>
              </OutlineButton>
            )}
          </div>
        )}

        {/* Pagination */}
        {effectiveTotalCount > 0 && (
          <div className="flex items-center justify-between px-6 py-4 border-t gap-4 mt-6">
            {/* Left side - Total count */}
            <div className="flex items-center gap-2">
              <Text className="text-sm text-ui-fg-base">
                Total Arrivals: {effectiveTotalCount}
              </Text>
            </div>

            {/* Center - Page navigation */}
            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="w-8 h-8 p-0 flex items-center justify-center border bg-ui-bg-base text-ui-fg-base border-ui-border-base hover:bg-ui-bg-subtle"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              {/* Page numbers */}
              <div className="flex items-center gap-1">
                {(() => {
                  const maxVisiblePages = 5;
                  const startPage = Math.max(
                    1,
                    currentPage - Math.floor(maxVisiblePages / 2)
                  );
                  const endPage = Math.min(
                    totalPages,
                    startPage + maxVisiblePages - 1
                  );
                  const adjustedStartPage = Math.max(
                    1,
                    endPage - maxVisiblePages + 1
                  );

                  const pages = [];
                  for (let i = adjustedStartPage; i <= endPage; i++) {
                    pages.push(
                      <Button
                        key={i}
                        variant={i === currentPage ? "primary" : "secondary"}
                        size="small"
                        onClick={() => handlePageChange(i)}
                        className={`w-8 h-8 p-0 flex items-center justify-center border ${
                          i === currentPage
                            ? "bg-ui-bg-interactive text-ui-fg-on-color border-ui-bg-interactive"
                            : "bg-ui-bg-base text-ui-fg-base border-ui-border-base hover:bg-ui-bg-subtle"
                        }`}
                      >
                        {i}
                      </Button>
                    );
                  }
                  return pages;
                })()}
              </div>

              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="w-8 h-8 p-0 flex items-center justify-center border bg-ui-bg-base text-ui-fg-base border-ui-border-base hover:bg-ui-bg-subtle"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            {/* Right side - Show per page dropdown */}
            <div className="flex items-center gap-2">
              <Text className="text-sm text-ui-fg-base">Show per Page:</Text>
              <Select
                value={limit.toString()}
                onValueChange={(value) => {
                  const newLimit = parseInt(value);
                  updateParamsImmediate({
                    limit: newLimit.toString(),
                    page: "1", // Reset to first page when changing page size
                  });
                }}
              >
                <Select.Trigger className="w-[60px] h-8 border-ui-border-base bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle">
                  <Select.Value />
                </Select.Trigger>
                <Select.Content className="bg-ui-bg-base border-ui-border-base">
                  <Select.Item
                    value="5"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    5
                  </Select.Item>
                  <Select.Item
                    value="10"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    10
                  </Select.Item>
                  <Select.Item
                    value="25"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    25
                  </Select.Item>
                  <Select.Item
                    value="50"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    50
                  </Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>
        )}
      </Container>

      {/* Overlay and Drawer for Add Hotel */}
      {open && (
        <div
          className="fixed inset-0 bg-black/30 dark:bg-black/50"
          onClick={() => updateOpen(false)}
        />
      )}
      <FocusModal open={open} onOpenChange={updateOpen}>
        <FocusModal.Content className="shadow-lg flex flex-col">
          <HotelFormModern
            formData={formData}
            onSubmit={handleCreate}
            closeModal={() => updateOpen(false)}
            onSubmitRef={formSubmitRef}
          />
        </FocusModal.Content>
      </FocusModal>

      <BulkImportModal
        open={bulkImportOpen}
        onClose={() => {
          updateBulkImportOpen(false);
        }}
      />

      <ExportModal
        open={exportModalOpen}
        onClose={() => updateExportModalOpen(false)}
      />
    </>
  );
};

// Make sure the config is properly exported
export const config = defineRouteConfig({
  label: "Hotels",
  icon: Buildings,
});

// Default export for the component
export default HotelsPage;
