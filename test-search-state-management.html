<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search State Management Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useMemo } = React;

        // Simulate URL search params
        const useSimulatedSearchParams = () => {
            const [params, setParams] = useState(new URLSearchParams(window.location.search));
            
            const updateParams = (updates) => {
                const newParams = new URLSearchParams(params);
                Object.entries(updates).forEach(([key, value]) => {
                    if (value === null || value === undefined || value === "") {
                        newParams.delete(key);
                    } else {
                        newParams.set(key, String(value));
                    }
                });
                setParams(newParams);
                
                // Update browser URL (for demo purposes)
                const newUrl = newParams.toString() ? 
                    `${window.location.pathname}?${newParams.toString()}` : 
                    window.location.pathname;
                window.history.replaceState({}, '', newUrl);
            };

            return [params, updateParams];
        };

        // Before Fix Component (Problematic)
        const SearchInputBefore = ({ title, description }) => {
            const [searchParams, updateParams] = useSimulatedSearchParams();
            const [searchInput, setSearchInput] = useState(searchParams.get("search_old") || "");
            const [debouncedQuery, setDebouncedQuery] = useState(searchParams.get("search_old") || "");
            const [updateCount, setUpdateCount] = useState(0);

            // Problematic: Updates URL immediately during typing
            useEffect(() => {
                const timer = setTimeout(() => {
                    setDebouncedQuery(searchInput);
                    updateParams({ search_old: searchInput });
                    setUpdateCount(prev => prev + 1);
                }, 300);
                return () => clearTimeout(timer);
            }, [searchInput]);

            return (
                <div className="p-6 bg-white rounded-lg shadow mb-6">
                    <h2 className="text-lg font-semibold mb-2 text-red-600">{title}</h2>
                    <p className="text-sm text-gray-600 mb-4">{description}</p>
                    <div className="relative">
                        <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input
                            type="text"
                            placeholder="Search hotels..."
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                            className="h-9 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                        />
                    </div>
                    <div className="mt-2 text-xs text-gray-500">
                        URL Updates: {updateCount} | Current: "{debouncedQuery}"
                    </div>
                </div>
            );
        };

        // After Fix Component (Improved)
        const SearchInputAfter = ({ title, description }) => {
            const [searchParams, updateParams] = useSimulatedSearchParams();
            
            const urlSearchValue = useMemo(() => {
                return searchParams.get("search_new") || "";
            }, [searchParams]);

            const [searchInput, setSearchInput] = useState(urlSearchValue);
            const [debouncedQuery, setDebouncedQuery] = useState(urlSearchValue);
            const [updateCount, setUpdateCount] = useState(0);
            const isUpdatingFromURL = useRef(false);

            // URL sync effect
            useEffect(() => {
                if (urlSearchValue !== searchInput && !isUpdatingFromURL.current) {
                    isUpdatingFromURL.current = true;
                    setSearchInput(urlSearchValue);
                    setDebouncedQuery(urlSearchValue);
                    setTimeout(() => {
                        isUpdatingFromURL.current = false;
                    }, 0);
                }
            }, [urlSearchValue, searchInput]);

            // Improved: Only updates URL when input differs from URL value
            useEffect(() => {
                if (isUpdatingFromURL.current) {
                    return;
                }

                const timer = setTimeout(() => {
                    if (searchInput !== urlSearchValue) {
                        setDebouncedQuery(searchInput);
                        updateParams({ search_new: searchInput });
                        setUpdateCount(prev => prev + 1);
                    }
                }, 300);

                return () => clearTimeout(timer);
            }, [searchInput, urlSearchValue]);

            return (
                <div className="p-6 bg-white rounded-lg shadow mb-6">
                    <h2 className="text-lg font-semibold mb-2 text-green-600">{title}</h2>
                    <p className="text-sm text-gray-600 mb-4">{description}</p>
                    <div className="relative">
                        <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input
                            type="text"
                            placeholder="Search hotels..."
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                            className="h-9 pl-10 pr-3 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                    </div>
                    <div className="mt-2 text-xs text-gray-500">
                        URL Updates: {updateCount} | Current: "{debouncedQuery}"
                    </div>
                </div>
            );
        };

        const App = () => {
            return (
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold mb-8">Search Input State Management Fix</h1>
                    
                    <SearchInputBefore 
                        title="❌ Before Fix (Problematic)"
                        description="Icon overlaps text, frequent URL updates during typing, potential performance issues"
                    />
                    
                    <SearchInputAfter 
                        title="✅ After Fix (Improved)"
                        description="Proper icon spacing, optimized URL updates, smooth typing experience"
                    />

                    <div className="p-6 bg-blue-50 rounded-lg">
                        <h2 className="text-lg font-semibold mb-4">🧪 Test Instructions</h2>
                        <ol className="list-decimal list-inside space-y-2 text-sm">
                            <li>Type in both search inputs and observe the "URL Updates" counter</li>
                            <li>Notice how the "Before" version updates more frequently</li>
                            <li>Notice how the "After" version has proper icon spacing (pl-10)</li>
                            <li>The "After" version only updates URL when you stop typing</li>
                            <li>Both maintain the 300ms debounce delay</li>
                        </ol>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
